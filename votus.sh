#!/bin/bash

# 设置错误处理
set -e

# 脚本名称
SCRIPT_NAME=$(basename "$0")

# 参数：输入目录和线程数
INPUT_DIR=$1
THREADS=$2

# 检查参数是否完整
if [[ -z "$INPUT_DIR" || -z "$THREADS" ]]; then
    echo "用法: bash $SCRIPT_NAME <输入目录> <线程数>"
    exit 1
fi

# 检查输入目录是否存在
if [[ ! -d "$INPUT_DIR" ]]; then
    echo "错误: 输入目录 '$INPUT_DIR' 不存在"
    exit 1
fi

# 检查输入目录中是否有所需文件
LYS_FILES=$(ls ${INPUT_DIR}/*_Lys_correct.fna 2>/dev/null | wc -l)
PRO_FILES=$(ls ${INPUT_DIR}/*_Pro_correct.fna 2>/dev/null | wc -l)

if [[ $LYS_FILES -eq 0 && $PRO_FILES -eq 0 ]]; then
    echo "错误: 在 '$INPUT_DIR' 中未找到任何 *_Lys_correct.fna 或 *_Pro_correct.fna 文件"
    exit 1
fi

# 检查cd-hit-est命令是否存在
if ! command -v cd-hit-est &> /dev/null; then
    echo "错误: 未找到 cd-hit-est 命令。请确保 CD-HIT 已正确安装并添加到 PATH 中"
    exit 1
fi

# 检查THREADS是否为正整数
if ! [[ "$THREADS" =~ ^[0-9]+$ ]] || [[ "$THREADS" -lt 1 ]]; then
    echo "错误: 线程数必须是正整数"
    exit 1
fi

# 创建输出目录
echo "创建输出目录..."
mkdir -p votu_output

echo "收集所有站点的高质量病毒序列..."

# 合并所有 Lys + Pro
if [[ $LYS_FILES -gt 0 || $PRO_FILES -gt 0 ]]; then
    cat ${INPUT_DIR}/*_Lys_correct.fna ${INPUT_DIR}/*_Pro_correct.fna > votu_output/all_sites_viruses.fna 2>/dev/null || true
    
    # 检查合并后的文件是否为空
    if [[ ! -s votu_output/all_sites_viruses.fna ]]; then
        echo "错误: 合并序列后的文件为空"
        exit 1
    fi
    
    echo "已合并序列数据，保存为 votu_output/all_sites_viruses.fna"
fi

# 确定系统可用内存（以MB为单位）
AVAILABLE_MEM=$(free -m | awk '/^Mem:/{print $7}')
CD_HIT_MEM=16000

# 如果可用内存不足，调整CD-HIT使用的内存
if [[ $AVAILABLE_MEM -lt $CD_HIT_MEM ]]; then
    CD_HIT_MEM=$((AVAILABLE_MEM - 1000))
    if [[ $CD_HIT_MEM -lt 1000 ]]; then
        echo "警告: 系统可用内存不足，CD-HIT可能无法正常工作"
        CD_HIT_MEM=1000
    fi
    echo "警告: 调整CD-HIT内存使用为 ${CD_HIT_MEM}MB"
fi

echo "开始CD-HIT聚类，生成vOTUs..."
echo "使用参数: -c 0.95 -aS 0.85 -G 0 -M ${CD_HIT_MEM} -T ${THREADS}"

# 运行CD-HIT聚类
if cd-hit-est -i votu_output/all_sites_viruses.fna \
              -o votu_output/vOTUs.fna \
              -c 0.95 -aS 0.85 -G 0 -M ${CD_HIT_MEM} -T ${THREADS}; then
    echo "聚类完成！结果输出至 votu_output/"
    echo "代表性vOTUs序列：votu_output/vOTUs.fna"
    echo "聚类信息文件：votu_output/vOTUs.fna.clstr"
    
    # 输出一些统计信息
    if command -v grep &> /dev/null && command -v wc &> /dev/null; then
        VOTU_COUNT=$(grep -c "^>" votu_output/vOTUs.fna 2>/dev/null || echo "未知")
        TOTAL_COUNT=$(grep -c "^>" votu_output/all_sites_viruses.fna 2>/dev/null || echo "未知")
        echo "原始序列数: $TOTAL_COUNT"
        echo "聚类后vOTU数: $VOTU_COUNT"
    fi
else
    echo "错误: CD-HIT聚类过程失败"
    exit 1
fi

echo "处理完成"