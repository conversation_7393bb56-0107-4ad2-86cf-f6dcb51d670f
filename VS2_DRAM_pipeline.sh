#!/bin/bash

# 显示脚本的使用方法
usage() {
  echo "
Usage: ${0} [-n|--Setname] [-o|--OutPath] [-t|--Threads]" 1>&2
  exit 1 
}

# 检查是否有参数输入，如果没有则显示用法
while [[ $# -eq 0 ]]; do usage; done

# 参数解析
while [[ $# -gt 0 ]];do
  key=${1}
  case ${key} in
    -n|--Setname) # 设置名称
      Setname=${2}
      shift 2
      ;;
    -o|--OutPath) # 输出路径
      OutPath=$(readlink -m "${2}")
      shift 2
      ;;
    -t|--Threads) # 线程数
      Threads=${2}
      shift 2
      ;;
    *)
      usage
      shift
      ;;
  esac
done

# 检查必需参数是否已设置
if [ -z "${Setname}" ] || [ -z "${OutPath}" ] || [ -z "${Threads}" ]; then
  echo "错误: 必需参数未设置。需要提供 Setname, OutPath 和 Threads。"
  usage
fi

# 定义工作目录
Contig="${OutPath}/1_Contig"
Virus="${OutPath}/2_Virus"
Process="${OutPath}/2_Virus/Process"

# 确保目录存在
mkdir -p "${Process}"

# 检查输入文件是否存在
if [ ! -f "${Contig}/${Setname}.contigs" ]; then
  echo "错误: 输入文件 ${Contig}/${Setname}.contigs 不存在。"
  exit 1
fi

# 激活VS2-DRAM环境
source /data/miniforge3/etc/profile.d/conda.sh
conda activate VS2-DRAM

echo "##########################  开始VS2分析  ##########################"
# 运行VirSorter2
virsorter run -i "${Contig}/${Setname}.contigs" -w "${Virus}/${Setname}_VS2" -j "${Threads}" --use-conda-off --prep-for-dramv --include-groups "dsDNAphage,NCLDV,ssDNA,lavidaviridae" --exclude-lt2gene -d /data/1_DataBase/VirSorter2
rm -rf "${Virus}/${Setname}_VS2/iter-0"

# 切换回META环境
conda activate META

echo "##########################  合并病毒序列  ##########################"
# 合并genomad和VS2的结果
cat "${Virus}/${Setname}_Genomad/${Setname}_summary/${Setname}_virus.fna" "${Virus}/${Setname}_VS2/final-viral-combined.fa" > "${Process}/${Setname}_VS2_Genomad.fna"

# 聚类去冗余
mmseqs easy-cluster "${Process}/${Setname}_VS2_Genomad.fna" "${Process}/${Setname}" "${Process}/mmseq" --cov-mode 1 -c 0.8 --cluster-mode 2 --min-seq-id 0.95 --remove-tmp-files --threads "${Threads}"

echo "##########################  开始CheckV分析  ##########################"
# 使用CheckV进行质量评估
checkv end_to_end "${Process}/${Setname}_rep_seq.fasta" "${Virus}/${Setname}_Checkv" -d /data/1_DataBase/checkv-db-v1.5/ -t "${Threads}"
rm -rf "${Virus}/${Setname}_Checkv/tmp" 

echo "##########################  筛选高质量病毒序列  ##########################"
# 筛选Genomad结果
awk -v FS="\t" -v OFS="\t" '$2 > 5000 && $7 >= 0.9 && $9 > 0' "${Virus}/${Setname}_Genomad/${Setname}_summary/${Setname}_virus_summary.tsv" > "${Process}/1_${Setname}_Correct_Genomad_hallmark_score90_more5k.tsv"
# 筛选VS2结果
awk -v FS="\t" -v OFS="\t" '$6 >= 0.9 && $9 > 0 && $8 > 5000' "${Virus}/${Setname}_VS2/final-viral-score.tsv" > "${Process}/1_${Setname}_Correct_VS2_hallmark_score90_more5k.tsv" 	

# 筛选溶源性病毒(Lys)
awk -v FS="\t" -v OFS="\t" '$3 == "No" && $2>=10000 && $6 > 0' "${Virus}/${Setname}_Checkv/quality_summary.tsv" > "${Process}/2_${Setname}_Lys_Correct_Checkv_more_10k_viralgene.tsv"
awk -v FS="\t" -v OFS="\t" '$3 == "No" && $2>=10000 && $7 == 0' "${Virus}/${Setname}_Checkv/quality_summary.tsv" > "${Process}/2_${Setname}_Lys_Correct_Checkv_more_10k_no_hostgene.tsv"  
awk -v FS="\t" -v OFS="\t" '($2>=5000 && $2<10000) && ($3=="No") && ($10!="NA" && $10>=50)' "${Virus}/${Setname}_Checkv/quality_summary.tsv" > "${Process}/2_${Setname}_Lys_Correct_Checkv_5-10k_viralgene_completeness_50.tsv"
awk -v FS="\t" -v OFS="\t" '($2>=1500 && $2<5000) && ($3=="No") && $6 > 0 && ($10!="NA" && $10>=90)' "${Virus}/${Setname}_Checkv/quality_summary.tsv" > "${Process}/2_${Setname}_Lys_Correct_Checkv_1.5-5k_viralgene_completeness_90.tsv"  
awk -v FS="\t" -v OFS="\t" '($2>=1500 && $2<=10000) && ($3=="No") && $7 == 0 && $11 ~ "DTR"' "${Virus}/${Setname}_Checkv/quality_summary.tsv" > "${Process}/2_${Setname}_Lys_Correct_Checkv_1.5-10k_no_hostgene_DTR.tsv"

# 筛选产噬菌体(Pro)
awk -v FS="\t" -v OFS="\t" '$3 == "Yes" && $2>=10000 && $6 > 0' "${Virus}/${Setname}_Checkv/quality_summary.tsv" > "${Process}/2_${Setname}_Pro_Correct_Checkv_more_10k_viralgene.tsv"
awk -v FS="\t" -v OFS="\t" '$3 == "Yes" && $2>=10000 && $7 == 0' "${Virus}/${Setname}_Checkv/quality_summary.tsv" > "${Process}/2_${Setname}_Pro_Correct_Checkv_more_10k_no_hostgene.tsv"  
awk -v FS="\t" -v OFS="\t" '($2>=5000 && $2<10000) && ($3=="Yes") && ($10!="NA" && $10>=50)' "${Virus}/${Setname}_Checkv/quality_summary.tsv" > "${Process}/2_${Setname}_Pro_Correct_Checkv_5-10k_viralgene_completeness_50.tsv"
awk -v FS="\t" -v OFS="\t" '($2>=1500 && $2<5000) && ($3=="Yes") && $6 > 0 && ($10!="NA" && $10>=90)' "${Virus}/${Setname}_Checkv/quality_summary.tsv" > "${Process}/2_${Setname}_Pro_Correct_Checkv_1.5-5k_viralgene_completeness_90.tsv"  
awk -v FS="\t" -v OFS="\t" '($2>=1500 && $2<=10000) && ($3=="Yes") && $7 == 0 && $11 ~ "DTR"' "${Virus}/${Setname}_Checkv/quality_summary.tsv" > "${Process}/2_${Setname}_Pro_Correct_Checkv_1.5-10k_no_hostgene_DTR.tsv"

echo "##########################  合并筛选结果  ##########################"
# 合并筛选结果
cat "${Process}/1_${Setname}_Correct_Genomad_hallmark_score90_more5k.tsv" "${Process}/1_${Setname}_Correct_VS2_hallmark_score90_more5k.tsv" > "${Process}/1_${Setname}_Correct_hallmark_score90.tsv"

# 分离溶源性和产噬菌体
awk -v FS="\t" -v OFS="\t" 'NR==FNR{a[$1];next} ($1) in a' "${Process}/1_${Setname}_Correct_hallmark_score90.tsv" "${Virus}/${Setname}_Checkv/quality_summary.tsv" | awk '$3=="No"' > "${Process}/2_${Setname}_Lys_hallmark_score90.tsv"
awk -v FS="\t" -v OFS="\t" 'NR==FNR{a[$1];next} ($1) in a' "${Process}/1_${Setname}_Correct_hallmark_score90.tsv" "${Virus}/${Setname}_Checkv/quality_summary.tsv" | awk '$3=="Yes"' > "${Process}/2_${Setname}_Pro_hallmark_score90.tsv"

# 提取序列ID
cat "${Process}"/2_${Setname}_Lys* | awk '{print $1}' | awk '!a[$0]++' > "${Process}/A_${Setname}_Lys_correct.list"
cat "${Process}"/2_${Setname}_Pro* | awk '{print $1}' | awk '!a[$0]++' > "${Process}/A_${Setname}_Pro_correct.list"

# 提取最终序列
seqkit grep -f "${Process}/A_${Setname}_Lys_correct.list" "${Process}/${Setname}_rep_seq.fasta" | sed "s/>/>${Setname}_Lys_|_/g" > "${Virus}/${Setname}_Lys_correct.fna"
seqkit grep -f "${Process}/A_${Setname}_Pro_correct.list" "${Process}/${Setname}_rep_seq.fasta" | sed "s/>/>${Setname}_Pro_|_/g" > "${Virus}/${Setname}_Pro_correct.fna"

echo "##########################  VS2-DRAM 分析完成  ##########################" 