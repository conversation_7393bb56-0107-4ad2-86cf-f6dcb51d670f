#!/usr/bin/env bash
set -euo pipefail

# --- 1. 配置 ---
INPUT_DIR="renamed_contigs"
OUTPUT_BASE_DIR="prodigal_results_parallel"
PARALLEL_JOBS=8  # 同时运行8个任务，请根据您的CPU核心数调整

# --- 2. 定义单个任务的函数 ---
run_prodigal_task() {
    input_file=$1
    
    # 提取样本名
    SAMPLE=$(basename "${input_file}" .fa)
    
    # 创建独立的输出目录
    SAMPLE_OUTPUT_DIR="${OUTPUT_BASE_DIR}/${SAMPLE}_prodigal"
    mkdir -p "${SAMPLE_OUTPUT_DIR}"
    
    echo "▶ 提交任务: ${SAMPLE}"
    
    # Prodigal 命令
    prodigal \
        -i "${input_file}" \
        -o "${SAMPLE_OUTPUT_DIR}/${SAMPLE}.gff" \
        -a "${SAMPLE_OUTPUT_DIR}/${SAMPLE}.faa" \
        -d "${SAMPLE_OUTPUT_DIR}/${SAMPLE}.fna" \
        -p meta \
        -g 11
}

# --- 3. 运行 ---
mkdir -p "${OUTPUT_BASE_DIR}"

# 导出函数和变量，以便 parallel 可以调用它们
export -f run_prodigal_task
export OUTPUT_BASE_DIR

# 查找所有输入文件，并通过管道传给 parallel 来执行
ls "${INPUT_DIR}"/*.fa | parallel -j "${PARALLEL_JOBS}" --bar run_prodigal_task {}

echo ""
echo "所有并行任务已提交/完成！"