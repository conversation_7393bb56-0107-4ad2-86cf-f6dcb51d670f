#!/bin/bash

mkdir -p ~/.parallel && touch ~/.parallel/will-cite

parallel -j4 --eta '
  sample={/%%_1.fastq.gz}
  fastp -i {1} -I {2} \
        -o fastp_out/{= s:_1.fastq.gz:_fp1.fq.gz: =} \
        -O fastp_out/{= s:_1.fastq.gz:_fp2.fq.gz: =} \
        -q 20 -u 30 -l 50 --detect_adapter_for_pe -w 8 \
        --html fastp_report/{/%%_1.fastq.gz}.html \
        --json fastp_report/{/%%_1.fastq.gz}.json
' ::: *_1.fastq.gz :::+ *_2.fastq.gz
