import pandas as pd
import os

# 提示用户输入文件夹路径
folder_path = input("请输入包含TSV文件的文件夹路径: ")

# 获取所有 .tsv 文件名
tsv_files = [f for f in os.listdir(folder_path) if f.endswith(".tsv")]

# 初始化一个空 DataFrame
merged_df = None

# 遍历所有文件
for file in tsv_files:
    file_path = os.path.join(folder_path, file)
    df = pd.read_csv(file_path, sep="\t")
    
    # 样品名从文件名提取（去掉扩展名）
    sample_name = os.path.splitext(file)[0]
    
    # 重命名 TPM 列为样品名
    df = df.rename(columns={"TPM": sample_name})
    
    # 合并（按 vOTU_ID 外连接）
    if merged_df is None:
        merged_df = df
    else:
        merged_df = pd.merge(merged_df, df, on="vOTU_ID", how="outer")

# 缺失值填 0
merged_df = merged_df.fillna(0)

# 保存合并结果
merged_df.to_csv("merged_votu_abundance.tsv", sep="\t", index=False)