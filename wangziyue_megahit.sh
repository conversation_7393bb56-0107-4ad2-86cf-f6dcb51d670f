#!/bin/bash

# 显示脚本的使用方法
usage() {
  echo "
Usage: ${0} [-1|--FQ1] [-2|--FQ2] [-n|--Setname] [-o|--OutPath] [-t|--Threads]" 1>&2
  exit 1 
}

# 检查是否有参数输入，如果没有则显示用法
while [[ $# -eq 0 ]]; do usage; done

# 参数解析
while [[ $# -gt 0 ]];do
  key=${1}
  case ${key} in
    -1|--FQ1) # 输入的FASTQ文件1
      FQ1=$(readlink -f "${2}")
      shift 2
      ;;
    -2|--FQ2) # 输入的FASTQ文件2
      FQ2=$(readlink -f "${2}")
      shift 2
      ;;
    -n|--Setname) # 设置名称
      Setname=${2}
      shift 2
      ;;
    -o|--OutPath) # 输出路径
      OutPath=$(readlink -m "${2}")
      shift 2
      ;;
	-t|--Threads)
	  Threads=${2}
      shift 2
      ;;
    *)
      usage
      shift
      ;;
  esac
done

# 激活META环境
source /data/miniforge3/etc/profile.d/conda.sh
conda activate META

# 创建工作目录
     QC=${OutPath}/0_QC
 Contig=${OutPath}/1_Contig
  Virus=${OutPath}/2_Virus 
Process=${OutPath}/2_Virus/Process
mkdir -p ${OutPath} ${QC} ${Contig} ${Virus} ${Process}

# FASTQ文件的质量控制
fastp -n 0 -q 20 -u 20 -i ${FQ1} -o ${QC}/20-${Setname}-1.fq.gz -I ${FQ2} -O ${QC}/20-${Setname}-2.fq.gz &&
rm -rf ${FQ1} ${FQ2}
fastp -n 0 -q 30 -u 30 -h ${QC}/${Setname}.html -R ${QC}/${Setname}.report -i ${QC}/20-${Setname}-1.fq.gz -o ${QC}/QC-${Setname}_1.fq.gz -I ${QC}/20-${Setname}-2.fq.gz -O ${QC}/QC-${Setname}_2.fq.gz &&
rm -rf ${QC}/20-${Setname}-1.fq.gz ${QC}/20-${Setname}-2.fq.gz 
QCFQ1=${QC}/QC-${Setname}_1.fq.gz
QCFQ2=${QC}/QC-${Setname}_2.fq.gz
echo '##########################  QC done  ##########################'