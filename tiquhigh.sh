#!/usr/bin/env bash
# -------------------------------------------------------------
#  extract_high_quality_votus.sh
#  从 CheckV 的 quality_summary.tsv 筛选 Complete / High-quality
#  vOTU 代表序列，并从 result_rep_seq.fasta 中提取对应序列。
# -------------------------------------------------------------
#  用法：
#    bash extract_high_quality_votus.sh [quality.tsv] [rep.fasta] [out.fasta]
#  如果不传参数，采用默认文件名：
#    quality_summary.tsv  result_rep_seq.fasta  HQ_vOTUs.fasta
# -------------------------------------------------------------

set -euo pipefail

# ----------- 输入参数与默认值 ----------
QUALITY_TSV=${1:-quality_summary.tsv}
REP_FASTA=${2:-result_rep_seq.fasta}
OUT_FASTA=${3:-HQ_vOTUs.fasta}

# ----------- 临时文件 ----------
ID_LIST=$(mktemp)

# ----------- 提取满足条件的序列 ID ----------
awk -F'\t' '
  NR==1 {
      # 解析标题行，找出 "quality" 列
      for(i=1;i<=NF;i++){
          if($i=="quality"){qc=i}
      }
      if(qc==0){qc=NF}     # 若未找到标题，则默认最后一列
      next
  }
  ($qc=="Complete" || $qc=="High-quality") {print $1}
' "$QUALITY_TSV" > "$ID_LIST"

# ----------- 提取序列 ----------
if command -v seqkit > /dev/null 2>&1; then
    seqkit grep -f "$ID_LIST" "$REP_FASTA" -o "$OUT_FASTA"
else
    echo "[WARN] 找不到 seqkit，尝试使用 seqtk"
    command -v seqtk > /dev/null 2>&1 || { echo "[ERROR] 既无 seqkit 也无 seqtk，无法继续"; exit 1; }
    seqtk subseq "$REP_FASTA" "$ID_LIST" > "$OUT_FASTA"
fi

# ----------- 清理 ----------
rm -f "$ID_LIST"

echo "✅ 已生成高质量 vOTU FASTA：$OUT_FASTA"