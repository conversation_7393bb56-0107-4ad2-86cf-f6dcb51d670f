#!/bin/bash

# 显示脚本的使用方法
usage() {
  echo "
用法: ${0} [-n|--Setname] [-o|--OutPath] [-p|--Prefix]" 1>&2
  exit 1 
}

# 检查是否有参数输入，如果没有则显示用法
while [[ $# -eq 0 ]]; do usage; done

# 参数解析
while [[ $# -gt 0 ]];do
  key=${1}
  case ${key} in
    -n|--Setname) # 设置名称
      Setname=${2}
      shift 2
      ;;
    -o|--OutPath) # 输出路径
      OutPath=$(readlink -m "${2}")
      shift 2
      ;;
    -p|--Prefix) # 序列ID前缀
      Prefix=${2}
      shift 2
      ;;
    *)
      usage
      shift
      ;;
  esac
done

# 检查必需参数是否已设置
if [ -z "${Setname}" ] || [ -z "${OutPath}" ]; then
  echo "错误: 必需参数未设置。需要提供 Setname 和 OutPath。"
  usage
fi

# 如果前缀未设置，则使用Setname
if [ -z "${Prefix}" ]; then
  Prefix="${Setname}"
fi

# 定义工作目录
Virus="${OutPath}/Virus"
Process="${OutPath}/Virus/Process"

# 确保目录存在
mkdir -p "${Process}"

# 检查 Genomad 和 VS2 结果是否已存在
if [ ! -d "${Virus}/${Setname}_Genomad" ]; then
  echo "错误: Genomad 结果目录不存在。请先运行 Genomad 分析。"
  exit 1
fi

if [ ! -d "${Virus}/${Setname}_VS2" ]; then
  echo "错误: VirSorter2 结果目录不存在。请先运行 VirSorter2 分析。"
  exit 1
fi

echo "##########################  筛选病毒序列  ##########################"
# 筛选Genomad结果 (分数>=0.9，含有hallmark基因)
awk -v FS="\t" -v OFS="\t" '$7 >= 0.9 && $9 > 0' "${Virus}/${Setname}_Genomad/${Setname}_summary/${Setname}_virus_summary.tsv" > "${Process}/${Setname}_filtered_genomad.tsv"
echo "Genomad筛选后的病毒序列数量: $(wc -l < "${Process}/${Setname}_filtered_genomad.tsv")"

# 筛选VS2结果 (分数>=0.9，含有hallmark基因)
awk -v FS="\t" -v OFS="\t" '$6 >= 0.9 && $9 > 0' "${Virus}/${Setname}_VS2/final-viral-score.tsv" > "${Process}/${Setname}_filtered_vs2.tsv"
echo "VirSorter2筛选后的病毒序列数量: $(wc -l < "${Process}/${Setname}_filtered_vs2.tsv")"

# 提取序列ID
cut -f1 "${Process}/${Setname}_filtered_genomad.tsv" > "${Process}/${Setname}_genomad_ids.txt"
cut -f1 "${Process}/${Setname}_filtered_vs2.tsv" > "${Process}/${Setname}_vs2_ids.txt"

echo "##########################  合并病毒序列  ##########################"
# 提取genomad的病毒序列
if [ -s "${Process}/${Setname}_genomad_ids.txt" ]; then
  if command -v seqkit &> /dev/null; then
    seqkit grep -f "${Process}/${Setname}_genomad_ids.txt" "${Virus}/${Setname}_Genomad/${Setname}_summary/${Setname}_virus.fna" > "${Process}/${Setname}_filtered_genomad.fna"
  else
    echo "警告: seqkit未安装，尝试使用bioawk提取序列"
    if command -v bioawk &> /dev/null; then
      bioawk -cfastx 'BEGIN{while((getline<"'"${Process}/${Setname}_genomad_ids.txt"'")>0)ids[$1]=1} ids[$name]{print ">"$name; print $seq}' "${Virus}/${Setname}_Genomad/${Setname}_summary/${Setname}_virus.fna" > "${Process}/${Setname}_filtered_genomad.fna"
    else
      echo "错误: 需要安装seqkit或bioawk来提取序列"
      exit 1
    fi
  fi
else
  touch "${Process}/${Setname}_filtered_genomad.fna"
fi

# 提取VS2的病毒序列
if [ -s "${Process}/${Setname}_vs2_ids.txt" ]; then
  if command -v seqkit &> /dev/null; then
    seqkit grep -f "${Process}/${Setname}_vs2_ids.txt" "${Virus}/${Setname}_VS2/final-viral-combined.fa" > "${Process}/${Setname}_filtered_vs2.fna"
  else
    echo "警告: seqkit未安装，尝试使用bioawk提取序列"
    if command -v bioawk &> /dev/null; then
      bioawk -cfastx 'BEGIN{while((getline<"'"${Process}/${Setname}_vs2_ids.txt"'")>0)ids[$1]=1} ids[$name]{print ">"$name; print $seq}' "${Virus}/${Setname}_VS2/final-viral-combined.fa" > "${Process}/${Setname}_filtered_vs2.fna"
    else
      echo "错误: 需要安装seqkit或bioawk来提取序列"
      exit 1
    fi
  fi
else
  touch "${Process}/${Setname}_filtered_vs2.fna"
fi

# 合并两个结果
cat "${Process}/${Setname}_filtered_genomad.fna" "${Process}/${Setname}_filtered_vs2.fna" > "${Process}/${Setname}_combined.fna"

# 修改序列ID (修复重复标题问题)
awk -v prefix="${Prefix}" '/^>/{
  # 获取原始序列ID（移除">"符号）
  header=$0;
  gsub("^>", "", header);
  # 创建新的唯一标识符，保留原始ID信息
  print ">" prefix "_" NR "_" header;
  next
} {print}' "${Process}/${Setname}_combined.fna" > "${Virus}/${Setname}_final_virus.fna"

echo "##########################  序列统计  ##########################"
echo "最终病毒序列数量：$(grep -c ">" "${Virus}/${Setname}_final_virus.fna")"

echo "##########################  分析完成  ##########################"
echo "结果文件路径: ${Virus}/${Setname}_final_virus.fna" 