#!/bin/bash
nohup sortmerna --index 2 \
 --ref /data/1_DataBase/SortMeRNA/silva-euk-28s-id98.fasta \
 --ref /data/1_DataBase/SortMeRNA/silva-euk-18s-id95.fasta \
 --ref /data/1_DataBase/SortMeRNA/silva-bac-23s-id98.fasta \
 --ref /data/1_DataBase/SortMeRNA/silva-bac-16s-id90.fasta \
 --ref /data/1_DataBase/SortMeRNA/silva-arc-23s-id98.fasta \
 --ref /data/1_DataBase/SortMeRNA/silva-arc-16s-id95.fasta \
 --ref /data/1_DataBase/SortMeRNA/rfam-5s-database-id98.fasta \
 --ref /data/1_DataBase/SortMeRNA/rfam-5.8s-database-id98.fasta \
 --reads /data/Master/coldsea-24-grj/RNA/tri/SRR13819039_t1_paired.fq.gz \
 --reads /data/Master/coldsea-24-grj/RNA/tri/SRR13819039_t2_paired.fq.gz \
 --fastx \
 --paired_in \
 --threads 8 \
 --out2 \
 --aligned /data/Master/coldsea-24-grj/RNA/tri/workdir/aligned \
 --other /data/Master/coldsea-24-grj/RNA/tri/workdir/filtered \
 --workdir /data/Master/coldsea-24-grj/RNA/tri/workdir  > sortmerna.log 2>&1 &
 #这个脚本是用来建立索引的，然后输入双端测序运行sortmerna