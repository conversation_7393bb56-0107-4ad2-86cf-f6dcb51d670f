#!/usr/bin/env bash
set -euo pipefail         # 遇到错误立即退出（推荐）

DATA_DIR="/data/Master/coldsea-24-grj/RNA/tri"
for R1 in "${DATA_DIR}"/*_t1_paired.fq.gz; do
    sample=$(basename "${R1}" _t1_paired.fq.gz)
    R2="${DATA_DIR}/${sample}_t2_paired.fq.gz"

    echo "▶ 处理样品: ${sample}"
    ## ======== 在下方写你的命令 =========
    mkdir -p /data/Master/coldsea-24-grj/RNA/tri/workdir/${sample}  
    sortmerna --index 2 \
 --ref /data/1_DataBase/SortMeRNA/silva-euk-28s-id98.fasta \
 --ref /data/1_DataBase/SortMeRNA/silva-euk-18s-id95.fasta \
 --ref /data/1_DataBase/SortMeRNA/silva-bac-23s-id98.fasta \
 --ref /data/1_DataBase/SortMeRNA/silva-bac-16s-id90.fasta \
 --ref /data/1_DataBase/SortMeRNA/silva-arc-23s-id98.fasta \
 --ref /data/1_DataBase/SortMeRNA/silva-arc-16s-id95.fasta \
 --ref /data/1_DataBase/SortMeRNA/rfam-5s-database-id98.fasta \
 --ref /data/1_DataBase/SortMeRNA/rfam-5.8s-database-id98.fasta \
 --reads "${R1}" \
 --reads "${R2}" \
 --fastx \
 --paired_in \
 --threads 8 \
 --out2 \
 --aligned /data/Master/coldsea-24-grj/RNA/tri/workdir/${sample}/aligned \
 --other /data/Master/coldsea-24-grj/RNA/tri/workdir/${sample}/filtered \
 --workdir /data/Master/coldsea-24-grj/RNA/tri/workdir/${sample} 
    ## ===================================
done
#循环运行sortmerna