#!/bin/bash
python /data/miniforge3/pkgs/dram-1.5.0-pyhdfd78af_0/python-scripts/DRAM-v.py annotate \
 -i /data/Master/coldsea-24-grj/southsea/final/virsorter2/for-dramv/final-viral-combined-for-dramv.fa \
 -v /data/Master/coldsea-24-grj/southsea/final/virsorter2/for-dramv/viral-affi-contigs-for-dramv.tab \
 -o /data/Master/coldsea-24-grj/southsea/final/virsorter2/for-dramv/dramv_out \
 --min_contig_size 1500 \
 --threads 12 \
 --skip_trnascan