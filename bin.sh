#!/bin/bash

# 样本列表（14个样本）
samples=(
  ROV2_0-3 ROV2_3-6 ROV2_6-9 ROV2_9-12 ROV2_12-15 ROV2_15-18 ROV2_18-21
  ROV5_0-3 ROV5_3-6 ROV5_6-9 ROV5_9-12 ROV5_12-15 ROV5_15-18 ROV5_18-21
)

# 固定路径
BASE="/data/Master/coldsea-24-grj/southsea/MAGs"
QC_DIR="${BASE}/QC"
CONTIG_DIR="${BASE}/contig"
OUT_DIR="${BASE}/binning"
THREADS=16

mkdir -p "$OUT_DIR"

# 循环处理每个样本
for sample in "${samples[@]}"; do
    echo "🧪 正在处理样本：$sample"

    READ1="${QC_DIR}/${sample}/${sample}_1.fq.gz"
    READ2="${QC_DIR}/${sample}/${sample}_2.fq.gz"
    CONTIGS="${CONTIG_DIR}/${sample}.contigs.fa"
    SAMPLE_OUT="${OUT_DIR}/${sample}"

    # 创建输出目录
    mkdir -p "$SAMPLE_OUT"

    # MetaWRAP 分箱
    metawrap binning \
        -o "$SAMPLE_OUT" \
        -t $THREADS \
        -a "$CONTIGS" \
        --metabat2 --maxbin2 --concoct \
        "$READ1" "$READ2"
done