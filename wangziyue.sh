#!/bin/bash

# 显示脚本的使用方法
usage() {
  echo "
Usage: ${0} [-1|--FQ1] [-2|--FQ2] [-n|--Setname] [-o|--OutPath] [-t|--Threads]" 1>&2
  exit 1 
}

# 检查是否有参数输入，如果没有则显示用法
while [[ $# -eq 0 ]]; do usage; done

# 参数解析
while [[ $# -gt 0 ]];do
  key=${1}
  case ${key} in
    -1|--FQ1) # 输入的FASTQ文件1
      FQ1=$(readlink -f "${2}")
      shift 2
      ;;
    -2|--FQ2) # 输入的FASTQ文件2
      FQ2=$(readlink -f "${2}")
      shift 2
      ;;
    -n|--Setname) # 设置名称
      Setname=${2}
      shift 2
      ;;
    -o|--OutPath) # 输出路径
      OutPath=$(readlink -m "${2}")
      shift 2
      ;;
	-t|--Threads)
	  Threads=${2}
      shift 2
      ;;
    *)
      usage
      shift
      ;;
  esac
done

# 激活META环境
source /data/miniforge3/etc/profile.d/conda.sh
conda activate META

# 创建工作目录
     QC=${OutPath}/0_QC
 Contig=${OutPath}/1_Contig
  Virus=${OutPath}/2_Virus 
Process=${OutPath}/2_Virus/Process
mkdir -p ${OutPath} ${QC} ${Contig} ${Virus} ${Process}

# FASTQ文件的质量控制
fastp -n 0 -q 20 -u 20 -i ${FQ1} -o ${QC}/20-${Setname}-1.fq.gz -I ${FQ2} -O ${QC}/20-${Setname}-2.fq.gz &&
rm -rf ${FQ1} ${FQ2}
fastp -n 0 -q 30 -u 30 -h ${QC}/${Setname}.html -R ${QC}/${Setname}.report -i ${QC}/20-${Setname}-1.fq.gz -o ${QC}/QC-${Setname}_1.fq.gz -I ${QC}/20-${Setname}-2.fq.gz -O ${QC}/QC-${Setname}_2.fq.gz &&
rm -rf ${QC}/20-${Setname}-1.fq.gz ${QC}/20-${Setname}-2.fq.gz 
QCFQ1=${QC}/QC-${Setname}_1.fq.gz
QCFQ2=${QC}/QC-${Setname}_2.fq.gz
echo '##########################  QC done  ##########################'

# 使用metaspades进行基因组组装
metaspades.py -1 ${QCFQ1} -2 ${QCFQ2} -t ${Threads} -m 2048 -o ${Contig}/${Setname} &&
mv ${Contig}/${Setname}/contigs.fasta ${Contig}/${Setname}.contigs &&
rm -rf ${Contig}/${Setname}/
echo '##########################  spades done  ##########################'


genomad end-to-end --threads ${Threads} ${Contig}/${Setname}.contigs ${Virus}/${Setname}_Genomad /data/1_DataBase/genomad_db/ &&
rm -rf ${Virus}/${Setname}_Genomad/${Setname}_nn_classification ${Virus}/${Setname}_Genomad/${Setname}_annotate

conda activate VS2-DRAM
virsorter run -i ${Contig}/${Setname}.contigs -w ${Virus}/${Setname}_VS2 -j ${Threads} --use-conda-off --prep-for-dramv --include-groups "dsDNAphage,NCLDV,ssDNA,lavidaviridae" --exclude-lt2gene &&
rm -rf ${Virus}/${Setname}_VS2/iter-0

conda activate META
cat  ${Virus}/${Setname}_Genomad/${Setname}_summary/${Setname}_virus.fna ${Virus}/${Setname}_VS2/final-viral-combined.fa > ${Process}/${Setname}_VS2_Genomad.fna
mmseqs easy-cluster ${Process}/${Setname}_VS2_Genomad.fna ${Process}/${Setname} ${Process}/mmseq --cov-mode 1 -c 0.8 --cluster-mode 2 --min-seq-id 0.95 --remove-tmp-files --threads ${Threads} &&
checkv end_to_end ${Process}/${Setname}_rep_seq.fasta ${Virus}/${Setname}_Checkv -d /data/1_DataBase/checkv-db-v1.5/ -t ${Threads} &&
rm -rf ${Virus}/${Setname}_Checkv/tmp 


    awk -v FS="\t" -v OFS="\t" '$2 > 5000 && $7 >= 0.9 && $9 > 0' ${Virus}/${Setname}_Genomad/${Setname}_summary/${Setname}_virus_summary.tsv > ${Process}/1_${Setname}_Correct_Genomad_hallmark_score90_more5k.tsv
    awk -v FS="\t" -v OFS="\t" '$6 >= 0.9 && $9 > 0 && $8 > 5000' ${Virus}/${Setname}_VS2/final-viral-score.tsv > ${Process}/1_${Setname}_Correct_VS2_hallmark_score90_more5k.tsv 	

    awk -v FS="\t" -v OFS="\t" '$3 == "No" && $2>=10000 && $6 > 0' ${Virus}/${Setname}_Checkv/quality_summary.tsv > ${Process}/2_${Setname}_Lys_Correct_Checkv_more_10k_viralgene.tsv

    awk -v FS="\t" -v OFS="\t" '$3 == "No" && $2>=10000 && $7 = 0' ${Virus}/${Setname}_Checkv/quality_summary.tsv > ${Process}/2_${Setname}_Lys_Correct_Checkv_more_10k_no_hostgene.tsv  

    awk -v FS="\t" -v OFS="\t" '($2>=5000 && $2<10000) && ($3=="No") && ($10!="NA" && $10>=50)' ${Virus}/${Setname}_Checkv/quality_summary.tsv > ${Process}/2_${Setname}_Lys_Correct_Checkv_5-10k_viralgene_completeness_50.tsv

    awk -v FS="\t" -v OFS="\t" '($2>=1500 && $2<5000) && ($3=="No") && $6 > 0 && ($10!="NA" && $10>=90)' ${Virus}/${Setname}_Checkv/quality_summary.tsv > ${Process}/2_${Setname}_Lys_Correct_Checkv_1.5-5k_viralgene_completeness_90.tsv  

    awk -v FS="\t" -v OFS="\t" '($2>=1500 && $2<=10000) && ($3=="No") && $7 = 0 && $11 ~ "DTR"' ${Virus}/${Setname}_Checkv/quality_summary.tsv > ${Process}/2_${Setname}_Lys_Correct_Checkv_1.5-10k_no_hostgene_DTR.tsv

    awk -v FS="\t" -v OFS="\t" '$3 == "Yes" && $2>=10000 && $6 > 0' ${Virus}/${Setname}_Checkv/quality_summary.tsv > ${Process}/2_${Setname}_Pro_Correct_Checkv_more_10k_viralgene.tsv

    awk -v FS="\t" -v OFS="\t" '$3 == "Yes" && $2>=10000 && $7 = 0' ${Virus}/${Setname}_Checkv/quality_summary.tsv > ${Process}/2_${Setname}_Pro_Correct_Checkv_more_10k_no_hostgene.tsv  

    awk -v FS="\t" -v OFS="\t" '($2>=5000 && $2<10000) && ($3=="Yes") && ($10!="NA" && $10>=50)' ${Virus}/${Setname}_Checkv/quality_summary.tsv > ${Process}/2_${Setname}_Pro_Correct_Checkv_5-10k_viralgene_completeness_50.tsv

    awk -v FS="\t" -v OFS="\t" '($2>=1500 && $2<5000) && ($3=="Yes") && $6 > 0 && ($10!="NA" && $10>=90)' ${Virus}/${Setname}_Checkv/quality_summary.tsv > ${Process}/2_${Setname}_Pro_Correct_Checkv_1.5-5k_viralgene_completeness_90.tsv  

    awk -v FS="\t" -v OFS="\t" '($2>=1500 && $2<=10000) && ($3=="Yes") && $7 = 0 && $11 ~ "DTR"' ${Virus}/${Setname}_Checkv/quality_summary.tsv > ${Process}/2_${Setname}_Pro_Correct_Checkv_1.5-10k_no_hostgene_DTR.tsv

  cat ${Process}/1_${Setname}_Correct_Genomad_hallmark_score90_more5k.tsv ${Process}/1_${Setname}_Correct_VS2_hallmark_score90_more5k.tsv > ${Process}/1_Correct_hallmark_score90.tsv
  awk -v FS="\t" -v OFS="\t" 'NR==FNR{a[$1];next} ($1) in a' ${Process}/1_Correct_hallmark_score90.tsv ${Virus}/${Setname}_Checkv/quality_summary.tsv | awk '$3=="No"' > ${Process}/2_${Setname}_Lys_hallmark_score90.tsv
  awk -v FS="\t" -v OFS="\t" 'NR==FNR{a[$1];next} ($1) in a' ${Process}/1_Correct_hallmark_score90.tsv ${Virus}/${Setname}_Checkv/quality_summary.tsv | awk '$3=="Yes"' > ${Process}/2_${Setname}_Pro_hallmark_score90.tsv

  cat ${Process}/2_${Setname}_Lys* | awk '{print $1}' | awk '!a[$0]++' > ${Process}/A_${Setname}_Lys_correct.list
  cat ${Process}/2_${Setname}_Pro* | awk '{print $1}' | awk '!a[$0]++' > ${Process}/A_${Setname}_Pro_correct.list
  seqkit grep -f ${Process}/A_${Setname}_Lys_correct.list ${Process}/${Setname}_rep_seq.fasta | sed "s/>/>${Setname}_Lys_|_/g" > ${Virus}/${Setname}_Lys_correct.fna
  seqkit grep -f ${Process}/A_${Setname}_Pro_correct.list ${Process}/${Setname}_rep_seq.fasta | sed "s/>/>${Setname}_Pro_|_/g" > ${Virus}/${Setname}_Pro_correct.fna