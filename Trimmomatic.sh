#!/usr/bin/env bash
set -euo pipefail

### ===== 可按需修改的参数 =====
THREADS=8                               # 每个样本用几个 CPU
MINLEN=70                               # 最短读长
ADAPTER="$CONDA_PREFIX/share/trimmomatic/adapters/TruSeq3-PE-2.fa"  # Illumina PE150 常用接头

### ===== 目录准备 =====
OUT_DIR="trim_out"   ; mkdir -p "${OUT_DIR}"
LOG_DIR="trim_log"   ; mkdir -p "${LOG_DIR}"

echo ">> Trimmomatic batch start ($(date))"
echo ">> Input dir : $(pwd)"
echo ">> Output dir: ${OUT_DIR}"
echo ">> Logs dir  : ${LOG_DIR}"
echo

### ===== 主循环：遍历每个 _fp1 文件 =====
for R1 in *_fp1.fq.gz; do
  # 判断目录下是否真的找到文件
  [[ -e "$R1" ]] || { echo "❗ 未找到 *_fp1.fq.gz，脚本退出" ; exit 1; }

  SAMPLE=${R1%%_fp1.fq.gz}          # 提取不含后缀的样本名
  R2="${SAMPLE}_fp2.fq.gz"          # 配对文件
  [[ -e "$R2" ]] || { echo "❗ 缺少配对文件 $R2" ; exit 1; }

  echo "▶ 处理样本 $SAMPLE"

  trimmomatic PE -threads "${THREADS}" -phred33 \
    "$R1" "$R2" \
    "${OUT_DIR}/${SAMPLE}_t1_paired.fq.gz"   "${OUT_DIR}/${SAMPLE}_t1_unpaired.fq.gz" \
    "${OUT_DIR}/${SAMPLE}_t2_paired.fq.gz"   "${OUT_DIR}/${SAMPLE}_t2_unpaired.fq.gz" \
    ILLUMINACLIP:"${ADAPTER}":2:30:10 \
    LEADING:3 TRAILING:3 SLIDINGWINDOW:4:30 MINLEN:"${MINLEN}" \
    2> "${LOG_DIR}/${SAMPLE}.log"

done

echo
echo ">> All done! 结果保存在 ${OUT_DIR}，日志保存在 ${LOG_DIR}"