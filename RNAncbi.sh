#!/bin/bash
# 一次写入 will-cite 标记
mkdir -p ~/.parallel && touch ~/.parallel/will-cite

# 离线转换：完全不会访问 NCBI，避免 TLS 报错
export NCBI_VDB_REMOTE_DISABLE=1
export NCBI_VDB_ENABLEREMOTE=0
export NCBI_SETTINGS=/dev/null

parallel -j2 --will-cite --ionice 2:7 --nice 10 '
  fname=$(basename {} .sra)
  fasterq-dump --threads 4 --mem 2G --split-files --no-reference --disable-telemetry {} &&
  pigz -p 4 -9 ${fname}_*.fastq &&
  rm -f {}
' ::: sra_cache/*.sra