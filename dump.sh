
#!/bin/bash
echo "正在创建输出文件夹 fastq_files..."
mkdir -p fastq_files

# 循环处理当前目录下的每一个.sra文件
for sra_file in *.sra
do
    echo "================================================="
    echo "正在处理文件: $sra_file"
    # 使用fastq-dump进行转换
    # --split-3 会自动处理双端和单端数据，并拆分双端数据为_1.fastq和_2.fastq
    # --gzip 直接输出为.gz压缩格式
    # -O 指定输出目录
    fastq-dump --split-3 --gzip -O fastq_files "$sra_file"
    echo "处理完成: $sra_file"
    echo "================================================="
done

echo "所有SRA文件已成功转换为fastq.gz格式！"