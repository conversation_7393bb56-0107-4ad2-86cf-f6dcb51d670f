for i in `cat sample.txt`; do  
  TMPDIR=. coverm contig \
    -1 /data/Master/coldsea-24-grj/southsea/fengdu/$i/${i}_1.fq.gz \
    -2 /data/Master/coldsea-24-grj/southsea/fengdu/$i/${i}_2.fq.gz \
    -r /data/Master/coldsea-24-grj/southsea/fengdu/votus.fasta \
    -p bwa-mem \
    --min-read-percent-identity 0.95 \
    --min-read-aligned-percent 0.75 \
    -m tpm \
    -t 30 \
    -o ./result/${i}.txt \
    > ./log_${i}.txt 2>&1
done