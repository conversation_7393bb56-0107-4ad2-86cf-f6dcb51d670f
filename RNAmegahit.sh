#!/usr/bin/env bash
#进行序列的拼接
set -euo pipefail # 遇到错误立即退出

# --- 1. 配置参数 ---

# 存放您66个fq.gz文件的目录 ( "." 代表当前目录)
DATA_DIR="."

# 存放所有拼接结果的总目录
OUTPUT_BASE_DIR="megahit_individual_assemblies"

# 计算资源设置
THREADS=16  # 每个拼接任务使用的线程数，根据服务器配置调整
# 内存使用比例，0.9 表示使用服务器总内存的90%。请根据实际情况调整
MEMORY_PERCENT=0.9

# --- 2. 循环运行 MEGAHIT ---

# 创建总输出目录
mkdir -p "${OUTPUT_BASE_DIR}"

# 循环遍历当前目录下所有 R1 文件
for R1 in "${DATA_DIR}"/*_R1.fq.gz; do
    
    # 从 R1 文件名中提取样品名 (例如 SRR15719349)
    SAMPLE=$(basename "${R1}" _R1.fq.gz)
    
    # 根据样品名构建对应的 R2 文件名
    R2="${DATA_DIR}/${SAMPLE}_R2.fq.gz"

    # 为每个样品创建一个独立的输出目录
    SAMPLE_OUTPUT_DIR="${OUTPUT_BASE_DIR}/${SAMPLE}_megahit_out"

    # 检查对应的R2文件是否存在
    if [ ! -f "${R2}" ]; then
        echo "警告: 找不到样品 ${SAMPLE} 对应的 R2 文件 (${R2})，跳过此样本。"
        continue
    fi

    echo "======================================================"
    echo "▶ 开始使用 MEGAHIT 拼接样本: ${SAMPLE}"
    echo "======================================================"

    # 运行 MEGAHIT 命令
    # -o 指定的输出目录必须不存在，脚本会自动创建
    megahit \
        -1 "${R1}" \
        -2 "${R2}" \
        -o "${SAMPLE_OUTPUT_DIR}" \
        -t "${THREADS}" \
    

    echo "✔ 样本 ${SAMPLE} 拼接完成！"
    echo "✔ 结果保存在: ${SAMPLE_OUTPUT_DIR}"
    echo ""

done

echo "所有样本的独立拼接任务已完成！"