#!/usr/bin/env bash
set -euo pipefail

# 输入数据库 fasta 的路径（只读目录）
REF_DIR="/data/1_DataBase/SortMeRNA"
# 输出索引存放目录（有写权限）
IDX_DIR="/data/Master/coldsea-24-grj/RNA/work/idx"

mkdir -p "$IDX_DIR"
cd "$IDX_DIR"

declare -a FASTA_FILES=(
"silva-euk-28s-id98.fasta"
"silva-euk-18s-id95.fasta"
"silva-bac-23s-id98.fasta"
"silva-bac-16s-id90.fasta"
"silva-arc-23s-id98.fasta"
"silva-arc-16s-id95.fasta"
"rfam-5s-database-id98.fasta"
"rfam-5.8s-database-id98.fasta"
)

for fasta in "${FASTA_FILES[@]}"; do
    base=$(basename "$fasta" .fasta)
    echo "▶ 正在建立索引: $base"
    indexdb_rna --ref "${REF_DIR}/${fasta},${IDX_DIR}/${base}"
done

echo "✅ 所有索引建立完成，存放在: $IDX_DIR"