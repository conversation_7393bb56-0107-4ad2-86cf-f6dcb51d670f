# 命令直接运行版本

# 设置参数
INPUT=final_seqs.fasta
OUTPUT_DIR=mmseq_result
TMP=${OUTPUT_DIR}/tmp

# 创建输出目录命令
mkdir -p ${OUTPUT_DIR}

# 核心聚类命令
mmseqs easy-cluster 5final.fna result/result result/tmp --cov-mode 1 -c 0.8 --cluster-mode 2 --min-seq-id 0.95 --remove-tmp-files 

# 命令执行后将生成:
# - ${OUTPUT_DIR}/result_rep_seq.fasta (代表序列文件)
# - ${OUTPUT_DIR}/result_cluster.tsv (聚类信息文件)

# 可选：提取所有聚类（每个聚类一个fasta文件）
# mmseqs createseqfiledb $DB $RESULT cluster_seqDB
# mmseqs result2flat $DB $DB cluster_seqDB cluster_seqs.fasta