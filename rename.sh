#!/usr/bin/env bash
#给拼接出来的contig重新命名
set -euo pipefail # 遇到错误立即退出，是编写脚本的好习惯

# --- 配置 ---
# 1. 包含所有样本拼接结果的总目录 (请确保这个路径是正确的)
BASE_DIR="/data/Master/coldsea-24-grj/RNA/megahit/megahit_individual_assemblies"

# 2. 存放重命名后文件的新目录 (脚本会自动创建)
OUTPUT_DIR="renamed_contigs"

# --- 脚本主体 ---
echo "创建输出目录: ${OUTPUT_DIR}"
mkdir -p "${OUTPUT_DIR}"

echo "开始查找并重命名文件..."

# 查找 BASE_DIR 下所有样本子目录中的 final.contigs.fa 文件
for original_file in "${BASE_DIR}"/*/final.contigs.fa; do
    # 检查文件是否存在，防止通配符没有匹配到任何文件时出错
    if [ -f "${original_file}" ]; then
        # 获取上一级目录的名称, 例如: SRR15719349_megahit_out
        dir_name=$(basename "$(dirname "${original_file}")")

        # 从目录名中提取SRR号, 例如: SRR15719349
        srr_id=${dir_name%_megahit_out}

        # 定义新的文件名, 例如: SRR15719349.fa
        new_name="${srr_id}.fa"

        # 打印操作信息，方便您查看脚本正在做什么
        echo "正在处理: ${original_file}"
        echo "  ==> 重命名为: ${OUTPUT_DIR}/${new_name}"

        # 执行移动并重命名操作 (mv)
        mv "${original_file}" "${OUTPUT_DIR}/${new_name}"
    fi
done

echo ""
echo "✔ 所有文件重命名完成！"
echo "✔ 结果已统一保存至 '${OUTPUT_DIR}' 目录。"